from deepeval import assert_test, login_with_confident_api_key
from deepeval.metrics import GEval
from deepeval.test_case import LLMTestCase, LLMTestCaseParams

login_with_confident_api_key("confident_us_7Xt8mxqAeTgB6lag2zdA5shz5QOzOGKVK2t9kt3abNs=")
import sys
import os

sys.path.append(os.path.join(os.path.dirname(__file__), "../"))

from evaluation.vertex_ai import vertexai_gemini

def test_case():
    correctness_metric = GEval(
        name="Correctness",
        criteria="Determine if the 'actual output' is correct based on the 'expected output'.",
        evaluation_params=[LLMTestCaseParams.ACTUAL_OUTPUT, LLMTestCaseParams.EXPECTED_OUTPUT],
        threshold=0.5,
        model=vertexai_gemini
    )
    test_case = LLMTestCase(
        input="What if these shoes don't fit?",
        # Replace this with the actual output from your LLM application
        actual_output="You have 30 days to get a full refund at no extra cost.",
        expected_output="We offer a 30-day full refund at no extra costs.",
        retrieval_context=["All customers are eligible for a 30 day full refund at no extra costs."]
    )
    assert_test(test_case, [correctness_metric])