{"test_cases_lookup_map": {"{\"actual_output\": \"You have 30 days to get a full refund at no extra cost.\", \"context\": null, \"expected_output\": \"We offer a 30-day full refund at no extra costs.\", \"hyperparameters\": null, \"input\": \"What if these shoes don't fit?\", \"retrieval_context\": [\"All customers are eligible for a 30 day full refund at no extra costs.\"]}": {"cached_metrics_data": [{"metric_data": {"name": "Correctness [GEval]", "threshold": 0.5, "success": false, "score": 0.0, "reason": "The actual output does not match the expected output. The sentences are different, failing on content and order.", "strictMode": false, "evaluationModel": "Vertex AI Model", "evaluationCost": 0, "verboseLogs": "Criteria:\nDetermine if the 'actual output' is correct based on the 'expected output'. \n \nEvaluation Steps:\n[\n    \"1. Verify that all elements present in the Expected Output are also present in the Actual Output.\",\n    \"2. Check if the elements in the Actual Output are in the same order as the Expected Output (if order matters).\",\n    \"3. Confirm that the values of corresponding elements in the Actual Output and Expected Output are identical.\",\n    \"4. <PERSON>ses<PERSON> if the Actual Output contains any extraneous elements not found in the Expected Output.\"\n] \n \nRubric:\nNone \n \nScore: 0.0"}, "metric_configuration": {"threshold": 0.5, "evaluation_model": "Vertex AI Model", "strict_mode": false, "criteria": "Determine if the 'actual output' is correct based on the 'expected output'.", "include_reason": false, "evaluation_steps": ["1. Verify that all elements present in the Expected Output are also present in the Actual Output.", "2. Check if the elements in the Actual Output are in the same order as the Expected Output (if order matters).", "3. Confirm that the values of corresponding elements in the Actual Output and Expected Output are identical.", "4. <PERSON><PERSON><PERSON> if the Actual Output contains any extraneous elements not found in the Expected Output."], "evaluation_params": ["actual_output", "expected_output"]}}]}}}